<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    title="关联资产包"
    @ok="handleSubmit"
    width="650px"
  >
    <div class="link-assets-content">
      <div class="content-item">
        <p class="item-label">公告标题：</p>
        <p class="item-content">{{ noticeInfo.name }}</p>
      </div>
      <div class="content-item">
        <p class="item-label">信息发布编码：</p>
        <p class="item-content">{{ noticeInfo.code }}</p>
      </div>
      <div class="content-item">
        <p class="item-label">招租方式：</p>
        <p class="item-content">{{ render.renderDict(`${noticeInfo.rentType}`, 'rent_type').children }}</p>
      </div>
      <div class="content-item">
        <div class="item-label">资产包：</div>
        <div class="item-content">
          <a-select
            v-model:value="selectedAssets"
            mode="multiple"
            :options="assetOptions"
            :filter-option="false"
            placeholder="请输入资产包名称搜索"
            :loading="loading"
            @search="handleSearch"
            style="width: 100%"
          />
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { getAssetPackageInfo, linkAssets, getRentInfoAssetPackageInfo } from '../notice.api';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { render } from '/@/utils/common/renderUtils';

  export default defineComponent({
    name: 'LinkAssetsModal',
    components: { BasicModal },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const { createMessage } = useMessage();
      const loading = ref(false);
      const selectedAssets = ref<string[]>([]);
      const selectedAssetsOrigin = ref<string[]>([]);
      const assetOptions = ref<any[]>([]);
      const noticeInfo = reactive({
        id: '',
        name: '',
        code: '',
        rentType: null,
      });

      // 弹窗注册
      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        setModalProps({ confirmLoading: false });

        // 重置数据
        selectedAssets.value = [];
        assetOptions.value = [];

        // 设置当前公告信息
        if (data?.record) {
          noticeInfo.id = data.record.id;
          noticeInfo.name = data.record.name;
          noticeInfo.code = data.record.code;
          noticeInfo.rentType = data.record.rentType;
          console.log(render.renderDict(`${noticeInfo.rentType}`, 'rent_type'), 'noticeInfo');
          // 如果已有关联资产，则设置已选择的资产
          if (data.record.linkedAssets) {
            selectedAssets.value = data.record.linkedAssets;
          }

          getAssetPackageInfoHandle(noticeInfo.id);

          // 初始加载资产选项
          await handleSearch('');
        }
      });

      function getAssetPackageInfoHandle(id) {
        getAssetPackageInfo({ id }).then((res) => {
          console.log(res, 'res');
          if (res && res.length > 0) {
            selectedAssets.value = res.map((item) => item.id);
            selectedAssetsOrigin.value = res;
          } else {
            selectedAssets.value = [];
          }
        });
      }

      // 搜索资产
      async function handleSearch(name: string) {
        loading.value = true;
        try {
          const res = await getRentInfoAssetPackageInfo({ name });
          assetOptions.value = res.map((item) => ({ value: item.id, label: item.name }));
        } catch (error) {
          console.error('获取资产包选项失败:', error);
        } finally {
          loading.value = false;
        }
      }

      // 提交表单
      async function handleSubmit() {
        if (!noticeInfo.id) {
          createMessage.error('未找到公告信息');
          return;
        }

        try {
          setModalProps({ confirmLoading: true });
          await linkAssets({
            noticeId: noticeInfo.id,
            rentIds: selectedAssets.value,
          });
          closeModal();
          emit('success');
        } catch (error) {
          console.error('关联资产包失败:', error);
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        registerModal,
        noticeInfo,
        selectedAssets,
        assetOptions,
        loading,
        handleSearch,
        handleSubmit,
        render,
      };
    },
  });
</script>

<style lang="less" scoped>
  .link-assets-content {
    padding: 10px 0;

    .content-item {
      margin-bottom: 20px;
      display: flex;

      .item-label {
        width: 120px;
        color: #606266;
        text-align: right;
        padding-right: 12px;
        font-weight: 500;
        line-height: 32px;
      }

      .item-content {
        flex: 1;
        line-height: 32px;
      }
    }
  }
</style> 